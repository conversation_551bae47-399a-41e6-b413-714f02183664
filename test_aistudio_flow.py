#!/usr/bin/env python3
"""
AI Studio 2 API 完整流程测试脚本

测试 chat <-> server <-> userscript <-> aistudio 的完整聊天功能
包括基本聊天、参数设置、模型选择等功能
"""

import asyncio
import json
import logging
import time
from typing import Any, Dict, List

import aiohttp
import websockets

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 配置
SERVER_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws/aistudio"

class AIStudioTestClient:
    """AI Studio 测试客户端"""
    
    def __init__(self):
        self.session = None
        self.ws = None
        self.connected = False
        self.models = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.ws:
            await self.ws.close()
        if self.session:
            await self.session.close()
    
    async def test_server_health(self) -> bool:
        """测试服务器健康状态"""
        logger.info("🏥 测试服务器健康状态...")
        
        try:
            async with self.session.get(f"{SERVER_URL}/") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ 服务器健康: {data}")
                    return True
                else:
                    logger.error(f"❌ 服务器健康检查失败: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"❌ 无法连接到服务器: {e}")
            return False
    
    async def simulate_userscript_connection(self) -> bool:
        """模拟用户脚本连接"""
        logger.info("🔌 模拟用户脚本连接...")
        
        try:
            self.ws = await websockets.connect(WS_URL)
            self.connected = True
            
            # 发送用户脚本就绪消息
            ready_message = {
                "type": "userscript_ready",
                "data": {
                    "version": "1.0.0",
                    "capabilities": [
                        "chat_completion",
                        "model_switching", 
                        "parameter_setting",
                        "stream_response"
                    ],
                    "models": [
                        {
                            "id": "gemini-1.5-pro",
                            "name": "Gemini 1.5 Pro",
                            "displayName": "Gemini 1.5 Pro",
                            "description": "最新的 Gemini Pro 模型"
                        },
                        {
                            "id": "gemini-1.5-flash",
                            "name": "Gemini 1.5 Flash", 
                            "displayName": "Gemini 1.5 Flash",
                            "description": "快速响应的 Gemini 模型"
                        },
                        {
                            "id": "gemini-1.0-pro",
                            "name": "Gemini 1.0 Pro",
                            "displayName": "Gemini 1.0 Pro", 
                            "description": "稳定的 Gemini Pro 模型"
                        }
                    ]
                }
            }
            
            await self.ws.send(json.dumps(ready_message))
            logger.info("✅ 用户脚本就绪消息已发送")
            
            # 等待服务器确认
            response = await asyncio.wait_for(self.ws.recv(), timeout=5.0)
            response_data = json.loads(response)
            
            if response_data.get("type") == "connection_status":
                logger.info("✅ 服务器确认连接成功")
                return True
            else:
                logger.warning(f"⚠️ 收到意外响应: {response_data}")
                return True  # 继续测试
                
        except Exception as e:
            logger.error(f"❌ 用户脚本连接失败: {e}")
            return False
    
    async def test_model_list_api(self) -> bool:
        """测试模型列表 API"""
        logger.info("📋 测试模型列表 API...")
        
        try:
            async with self.session.get(f"{SERVER_URL}/v1/models") as response:
                if response.status == 200:
                    data = await response.json()
                    self.models = data.get("data", [])
                    logger.info(f"✅ 获取到 {len(self.models)} 个模型")
                    for model in self.models:
                        logger.info(f"   - {model.get('id')}")
                    return True
                else:
                    logger.error(f"❌ 模型列表 API 失败: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"❌ 模型列表 API 错误: {e}")
            return False
    
    async def test_basic_chat(self) -> bool:
        """测试基本聊天功能"""
        logger.info("💬 测试基本聊天功能...")
        
        # 准备聊天请求
        chat_request = {
            "model": "gemini-1.5-pro",
            "messages": [
                {
                    "role": "user",
                    "content": "你好！请简单介绍一下你自己。"
                }
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        try:
            # 启动监听任务
            listen_task = asyncio.create_task(self.listen_for_userscript_messages())
            
            # 发送聊天请求
            async with self.session.post(
                f"{SERVER_URL}/v1/chat/completions",
                json=chat_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("✅ 聊天请求成功")
                    logger.info(f"   响应: {data}")
                    
                    # 取消监听任务
                    listen_task.cancel()
                    try:
                        await listen_task
                    except asyncio.CancelledError:
                        pass
                    
                    return True
                else:
                    logger.error(f"❌ 聊天请求失败: {response.status}")
                    text = await response.text()
                    logger.error(f"   错误详情: {text}")
                    
                    # 取消监听任务
                    listen_task.cancel()
                    try:
                        await listen_task
                    except asyncio.CancelledError:
                        pass
                    
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 聊天请求错误: {e}")
            return False
    
    async def listen_for_userscript_messages(self):
        """监听来自服务器的用户脚本消息"""
        try:
            while self.connected:
                message = await asyncio.wait_for(self.ws.recv(), timeout=1.0)
                data = json.loads(message)
                
                message_type = data.get("type")
                logger.info(f"📨 收到服务器消息: {message_type}")
                
                if message_type == "chat_request":
                    # 模拟用户脚本处理聊天请求
                    await self.simulate_chat_response(data)
                elif message_type == "model_switch":
                    # 模拟模型切换
                    await self.simulate_model_switch(data)
                elif message_type == "parameter_update":
                    # 模拟参数更新
                    await self.simulate_parameter_update(data)
                    
        except asyncio.TimeoutError:
            pass  # 正常超时，继续循环
        except Exception as e:
            logger.error(f"❌ 监听消息失败: {e}")
    
    async def simulate_chat_response(self, request_data: Dict[str, Any]):
        """模拟聊天响应"""
        request_id = request_data.get("request_id")
        messages = request_data.get("data", {}).get("messages", [])
        
        logger.info(f"🤖 模拟处理聊天请求 {request_id}")
        
        # 模拟处理延迟
        await asyncio.sleep(1)
        
        # 发送模拟响应
        response_message = {
            "type": "api_response",
            "request_id": request_id,
            "data": {
                "choices": [
                    {
                        "message": {
                            "role": "assistant",
                            "content": "你好！我是 Gemini，一个由 Google 开发的大型语言模型。我可以帮助你回答问题、进行对话、协助写作等多种任务。很高兴与你交流！"
                        },
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": 20,
                    "completion_tokens": 50,
                    "total_tokens": 70
                }
            }
        }
        
        await self.ws.send(json.dumps(response_message))
        logger.info("✅ 模拟聊天响应已发送")
    
    async def simulate_model_switch(self, request_data: Dict[str, Any]):
        """模拟模型切换"""
        model_id = request_data.get("data", {}).get("model")
        logger.info(f"🔄 模拟切换到模型: {model_id}")
        
        # 模拟切换延迟
        await asyncio.sleep(0.5)
        
        # 发送切换成功响应
        response_message = {
            "type": "model_switch_response",
            "request_id": request_data.get("request_id"),
            "data": {
                "success": True,
                "model": model_id,
                "message": f"已成功切换到模型 {model_id}"
            }
        }
        
        await self.ws.send(json.dumps(response_message))
        logger.info("✅ 模型切换响应已发送")
    
    async def simulate_parameter_update(self, request_data: Dict[str, Any]):
        """模拟参数更新"""
        params = request_data.get("data", {})
        logger.info(f"⚙️ 模拟更新参数: {params}")
        
        # 模拟更新延迟
        await asyncio.sleep(0.3)
        
        # 发送更新成功响应
        response_message = {
            "type": "parameter_update_response", 
            "request_id": request_data.get("request_id"),
            "data": {
                "success": True,
                "parameters": params,
                "message": "参数更新成功"
            }
        }
        
        await self.ws.send(json.dumps(response_message))
        logger.info("✅ 参数更新响应已发送")


async def main():
    """主测试函数"""
    logger.info("🚀 开始 AI Studio 2 API 完整流程测试")
    logger.info("=" * 60)
    
    async with AIStudioTestClient() as client:
        # 1. 测试服务器健康状态
        if not await client.test_server_health():
            logger.error("❌ 服务器健康检查失败，终止测试")
            return
        
        # 2. 模拟用户脚本连接
        if not await client.simulate_userscript_connection():
            logger.error("❌ 用户脚本连接失败，终止测试")
            return
        
        # 3. 测试模型列表 API
        if not await client.test_model_list_api():
            logger.error("❌ 模型列表 API 测试失败")
        
        # 4. 测试基本聊天功能
        if not await client.test_basic_chat():
            logger.error("❌ 基本聊天功能测试失败")
        
        logger.info("🎉 所有测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
