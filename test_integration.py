#!/usr/bin/env python3
"""
AI Studio 2 API 集成测试脚本

测试服务器启动、API 端点、WebSocket 连接等基本功能
"""

import asyncio
import json
import logging
import subprocess
import sys
import time
from pathlib import Path

import aiohttp
import websockets

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# 配置
SERVER_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws/aistudio"  # 使用 AI Studio WebSocket 端点


class IntegrationTester:
    """集成测试器"""

    def __init__(self):
        self.server_process = None
        self.session = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()

    def start_server(self) -> bool:
        """启动服务器"""
        logger.info("🚀 启动服务器...")

        try:
            # 检查是否已有服务器在运行
            if self.check_server_running():
                logger.info("✅ 服务器已在运行")
                return True

            # 启动新的服务器进程
            self.server_process = subprocess.Popen(
                [sys.executable, "-m", "server"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
            )

            # 等待服务器启动
            for i in range(10):
                time.sleep(1)
                if self.check_server_running():
                    logger.info("✅ 服务器启动成功")
                    return True
                logger.info(f"等待服务器启动... ({i + 1}/10)")

            logger.error("❌ 服务器启动超时")
            return False

        except Exception as e:
            logger.error(f"❌ 启动服务器失败: {e}")
            return False

    def check_server_running(self) -> bool:
        """检查服务器是否在运行"""
        try:
            import requests

            response = requests.get(f"{SERVER_URL}/", timeout=2)
            return response.status_code == 200
        except:
            return False

    async def test_server_health(self) -> bool:
        """测试服务器健康状态"""
        logger.info("🏥 测试服务器健康状态...")

        try:
            async with self.session.get(f"{SERVER_URL}/") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ 服务器健康: {data.get('status')}")
                    return True
                else:
                    logger.error(f"❌ 服务器健康检查失败: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"❌ 无法连接到服务器: {e}")
            return False

    async def test_model_list_without_userscript(self) -> bool:
        """测试没有用户脚本时的模型列表 API"""
        logger.info("📋 测试模型列表 API (无用户脚本)...")

        try:
            async with self.session.get(f"{SERVER_URL}/v1/models") as response:
                if response.status == 503:
                    logger.info("✅ 正确返回 503 (用户脚本未连接)")
                    return True
                else:
                    logger.warning(f"⚠️ 意外状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"❌ 模型列表 API 错误: {e}")
            return False

    async def test_websocket_connection(self) -> bool:
        """测试 WebSocket 连接"""
        logger.info("🔌 测试 WebSocket 连接...")

        try:
            # 连接 WebSocket
            ws = await websockets.connect(WS_URL)

            # 发送测试消息
            test_message = {
                "type": "userscript_ready",
                "data": {
                    "version": "test",
                    "capabilities": ["test"],
                    "models": [{"id": "test-model", "name": "Test Model", "displayName": "Test Model"}],
                },
            }

            await ws.send(json.dumps(test_message))
            logger.info("✅ WebSocket 消息发送成功")

            # 等待响应
            try:
                response = await asyncio.wait_for(ws.recv(), timeout=5.0)
                response_data = json.loads(response)
                logger.info(f"✅ 收到 WebSocket 响应: {response_data.get('type')}")
            except asyncio.TimeoutError:
                logger.warning("⚠️ WebSocket 响应超时")

            await ws.close()
            return True

        except Exception as e:
            logger.error(f"❌ WebSocket 连接失败: {e}")
            return False

    async def test_model_list_with_userscript(self) -> bool:
        """测试有用户脚本时的模型列表 API"""
        logger.info("📋 测试模型列表 API (有用户脚本)...")

        # 首先建立 WebSocket 连接并发送就绪消息
        try:
            ws = await websockets.connect(WS_URL)

            ready_message = {
                "type": "userscript_ready",
                "data": {
                    "version": "test",
                    "capabilities": ["test"],
                    "models": [
                        {"id": "gemini-1.5-pro", "name": "Gemini 1.5 Pro", "displayName": "Gemini 1.5 Pro"},
                        {"id": "gemini-1.5-flash", "name": "Gemini 1.5 Flash", "displayName": "Gemini 1.5 Flash"},
                    ],
                },
            }

            await ws.send(json.dumps(ready_message))

            # 等待一下让服务器处理
            await asyncio.sleep(1)

            # 现在测试模型列表 API
            async with self.session.get(f"{SERVER_URL}/v1/models") as response:
                if response.status == 200:
                    data = await response.json()
                    models = data.get("data", [])
                    logger.info(f"✅ 获取到 {len(models)} 个模型")
                    for model in models:
                        logger.info(f"   - {model.get('id')}")

                    await ws.close()
                    return True
                else:
                    logger.error(f"❌ 模型列表 API 失败: {response.status}")
                    await ws.close()
                    return False

        except Exception as e:
            logger.error(f"❌ 模型列表测试失败: {e}")
            return False

    async def test_chat_completion_without_userscript(self) -> bool:
        """测试没有用户脚本时的聊天完成 API"""
        logger.info("💬 测试聊天完成 API (无用户脚本)...")

        chat_request = {"model": "gemini-1.5-pro", "messages": [{"role": "user", "content": "Hello"}]}

        try:
            async with self.session.post(
                f"{SERVER_URL}/v1/chat/completions", json=chat_request, headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 503:
                    logger.info("✅ 正确返回 503 (用户脚本未连接)")
                    return True
                else:
                    logger.warning(f"⚠️ 意外状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"❌ 聊天完成 API 错误: {e}")
            return False

    def check_userscript_build(self) -> bool:
        """检查用户脚本是否已构建"""
        logger.info("🔨 检查用户脚本构建...")

        userscript_path = Path("dist/gemini2api.user.js")
        if userscript_path.exists():
            logger.info("✅ 用户脚本已构建")
            return True
        else:
            logger.error("❌ 用户脚本未构建，请运行 'npm run build'")
            return False

    def check_dependencies(self) -> bool:
        """检查依赖项"""
        logger.info("📦 检查依赖项...")

        try:
            import aiohttp
            import websockets

            logger.info("✅ Python 依赖项正常")
        except ImportError as e:
            logger.error(f"❌ 缺少 Python 依赖: {e}")
            return False

        # 检查 Node.js 依赖
        if not Path("node_modules").exists():
            logger.error("❌ Node.js 依赖未安装，请运行 'npm install'")
            return False

        logger.info("✅ Node.js 依赖项正常")
        return True


async def main():
    """主测试函数"""
    logger.info("🧪 开始 AI Studio 2 API 集成测试")
    logger.info("=" * 60)

    async with IntegrationTester() as tester:
        # 1. 检查依赖项
        if not tester.check_dependencies():
            logger.error("❌ 依赖项检查失败，终止测试")
            return

        # 2. 检查用户脚本构建
        if not tester.check_userscript_build():
            logger.error("❌ 用户脚本检查失败，终止测试")
            return

        # 3. 启动服务器
        if not tester.start_server():
            logger.error("❌ 服务器启动失败，终止测试")
            return

        # 4. 测试服务器健康状态
        if not await tester.test_server_health():
            logger.error("❌ 服务器健康检查失败")
            return

        # 5. 测试无用户脚本时的 API
        await tester.test_model_list_without_userscript()
        await tester.test_chat_completion_without_userscript()

        # 6. 测试 WebSocket 连接
        if not await tester.test_websocket_connection():
            logger.error("❌ WebSocket 连接测试失败")

        # 7. 测试有用户脚本时的 API
        if not await tester.test_model_list_with_userscript():
            logger.error("❌ 模型列表测试失败")

        logger.info("🎉 集成测试完成！")
        logger.info("📝 下一步:")
        logger.info("   1. 在浏览器中安装 Tampermonkey")
        logger.info("   2. 导入 dist/gemini2api.user.js")
        logger.info("   3. 访问 https://aistudio.google.com")
        logger.info("   4. 运行 test_aistudio_flow.py 进行完整测试")


if __name__ == "__main__":
    asyncio.run(main())
