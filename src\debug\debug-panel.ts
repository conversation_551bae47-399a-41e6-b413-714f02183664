/**
 * 调试面板
 * 提供实时的调试信息和状态监控
 */

import { logger } from '../utils/helpers';

export type LogLevel = 'info' | 'warn' | 'error' | 'debug';

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
}

export class DebugPanel {
  private panel: HTMLElement | null = null;
  private logContainer: HTMLElement | null = null;
  private statusContainer: HTMLElement | null = null;
  private isVisible: boolean = false;
  private logs: LogEntry[] = [];
  private maxLogs: number = 100;
  private isInitialized: boolean = false;

  constructor() {
    // 延迟初始化，等待 DOM 准备就绪
    this.initializeWhenReady();
  }

  /**
   * 等待 DOM 准备就绪后初始化
   */
  private async initializeWhenReady(): Promise<void> {
    // 避免重复初始化
    if (this.isInitialized) return;

    // 等待 document.body 可用
    await this.waitForBody();

    this.createPanel();
    this.attachEventListeners();
    this.isInitialized = true;

    // 渲染之前保存的日志
    this.renderAllLogs();
  }

  /**
   * 渲染所有保存的日志
   */
  private renderAllLogs(): void {
    if (!this.logContainer) return;

    // 清空容器
    this.logContainer.innerHTML = '';

    // 渲染所有日志
    this.logs.forEach(entry => this.renderLog(entry));
  }

  /**
   * 等待 document.body 可用
   */
  private async waitForBody(): Promise<void> {
    return new Promise<void>((resolve) => {
      if (document.body) {
        resolve();
        return;
      }

      // 如果 document.body 不存在，等待 DOM 加载
      const checkBody = () => {
        if (document.body) {
          resolve();
        } else {
          requestAnimationFrame(checkBody);
        }
      };

      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkBody, { once: true });
      } else {
        checkBody();
      }
    });
  }

  /**
   * 创建调试面板
   */
  private createPanel(): void {
    // 创建面板容器
    this.panel = document.createElement('div');
    this.panel.id = 'aistudio-debug-panel';
    this.panel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 400px;
      height: 500px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      border: 1px solid #333;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      z-index: 10000;
      display: none;
      flex-direction: column;
      overflow: hidden;
    `;

    // 创建标题栏
    const header = document.createElement('div');
    header.style.cssText = `
      background: #1a1a1a;
      padding: 8px 12px;
      border-bottom: 1px solid #333;
      display: flex;
      justify-content: space-between;
      align-items: center;
    `;
    header.innerHTML = `
      <span>🔍 AI Studio Debug Panel</span>
      <button id="debug-panel-close" style="background: #ff4444; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer;">×</button>
    `;

    // 创建状态区域
    this.statusContainer = document.createElement('div');
    this.statusContainer.style.cssText = `
      padding: 8px 12px;
      border-bottom: 1px solid #333;
      background: #2a2a2a;
      font-size: 11px;
    `;

    // 创建日志区域
    this.logContainer = document.createElement('div');
    this.logContainer.style.cssText = `
      flex: 1;
      overflow-y: auto;
      padding: 8px;
    `;

    // 创建控制区域
    const controls = document.createElement('div');
    controls.style.cssText = `
      padding: 8px 12px;
      border-top: 1px solid #333;
      background: #1a1a1a;
      display: flex;
      gap: 8px;
    `;
    controls.innerHTML = `
      <button id="debug-clear-logs" style="background: #666; color: white; border: none; border-radius: 3px; padding: 4px 8px; cursor: pointer; font-size: 11px;">Clear</button>
      <button id="debug-export-logs" style="background: #666; color: white; border: none; border-radius: 3px; padding: 4px 8px; cursor: pointer; font-size: 11px;">Export</button>
    `;

    // 组装面板
    this.panel.appendChild(header);
    this.panel.appendChild(this.statusContainer);
    this.panel.appendChild(this.logContainer);
    this.panel.appendChild(controls);

    // 添加到页面
    document.body.appendChild(this.panel);

    // 初始化状态
    this.updateStatus({
      connection: 'disconnected',
      models: 0,
      lastActivity: 'None',
    });
  }

  /**
   * 附加事件监听器
   */
  private attachEventListeners(): void {
    // 关闭按钮
    const closeBtn = document.getElementById('debug-panel-close');
    closeBtn?.addEventListener('click', () => this.hide());

    // 清空日志按钮
    const clearBtn = document.getElementById('debug-clear-logs');
    clearBtn?.addEventListener('click', () => this.clearLogs());

    // 导出日志按钮
    const exportBtn = document.getElementById('debug-export-logs');
    exportBtn?.addEventListener('click', () => this.exportLogs());

    // 键盘快捷键 (Ctrl+Shift+D)
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        e.preventDefault();
        this.toggle();
      }
    });
  }

  /**
   * 显示面板
   */
  public show(): void {
    if (!this.isInitialized) {
      // 如果还没初始化，等待初始化完成后再显示
      this.initializeWhenReady().then(() => {
        if (this.panel) {
          this.panel.style.display = 'flex';
          this.isVisible = true;
          logger.debug('调试面板已显示');
        }
      });
      return;
    }

    if (this.panel) {
      this.panel.style.display = 'flex';
      this.isVisible = true;
      logger.debug('调试面板已显示');
    }
  }

  /**
   * 隐藏面板
   */
  public hide(): void {
    if (!this.isInitialized) return;

    if (this.panel) {
      this.panel.style.display = 'none';
      this.isVisible = false;
      logger.debug('调试面板已隐藏');
    }
  }

  /**
   * 切换面板显示状态
   */
  public toggle(): void {
    if (!this.isInitialized) {
      this.show(); // 这会触发初始化
      return;
    }

    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * 添加日志
   */
  public addLog(level: LogLevel, message: string, data?: any): void {
    const entry: LogEntry = {
      timestamp: new Date().toLocaleTimeString(),
      level,
      message,
      data,
    };

    this.logs.push(entry);

    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // 如果面板还没初始化，只保存日志，不渲染
    if (this.isInitialized) {
      this.renderLog(entry);
    }
  }

  /**
   * 渲染单条日志
   */
  private renderLog(entry: LogEntry): void {
    if (!this.logContainer) return;

    const logElement = document.createElement('div');
    logElement.style.cssText = `
      margin-bottom: 4px;
      padding: 4px 8px;
      border-radius: 3px;
      background: ${this.getLogBackgroundColor(entry.level)};
      border-left: 3px solid ${this.getLogBorderColor(entry.level)};
    `;

    const icon = this.getLogIcon(entry.level);
    logElement.innerHTML = `
      <span style="color: #888;">[${entry.timestamp}]</span>
      <span style="color: ${this.getLogTextColor(entry.level)};">${icon} ${entry.message}</span>
      ${entry.data ? `<pre style="margin: 4px 0 0 0; font-size: 10px; color: #ccc; white-space: pre-wrap;">${JSON.stringify(entry.data, null, 2)}</pre>` : ''}
    `;

    this.logContainer.appendChild(logElement);

    // 自动滚动到底部
    this.logContainer.scrollTop = this.logContainer.scrollHeight;
  }

  /**
   * 获取日志背景色
   */
  private getLogBackgroundColor(level: LogLevel): string {
    switch (level) {
      case 'error': return 'rgba(255, 68, 68, 0.1)';
      case 'warn': return 'rgba(255, 193, 7, 0.1)';
      case 'info': return 'rgba(0, 123, 255, 0.1)';
      case 'debug': return 'rgba(108, 117, 125, 0.1)';
      default: return 'transparent';
    }
  }

  /**
   * 获取日志边框色
   */
  private getLogBorderColor(level: LogLevel): string {
    switch (level) {
      case 'error': return '#ff4444';
      case 'warn': return '#ffc107';
      case 'info': return '#007bff';
      case 'debug': return '#6c757d';
      default: return '#333';
    }
  }

  /**
   * 获取日志文本色
   */
  private getLogTextColor(level: LogLevel): string {
    switch (level) {
      case 'error': return '#ff6b6b';
      case 'warn': return '#ffd93d';
      case 'info': return '#74c0fc';
      case 'debug': return '#adb5bd';
      default: return '#fff';
    }
  }

  /**
   * 获取日志图标
   */
  private getLogIcon(level: LogLevel): string {
    switch (level) {
      case 'error': return '❌';
      case 'warn': return '⚠️';
      case 'info': return 'ℹ️';
      case 'debug': return '🔍';
      default: return '📝';
    }
  }

  /**
   * 更新状态信息
   */
  public updateStatus(status: {
    connection?: string;
    models?: number;
    lastActivity?: string;
    [key: string]: any;
  }): void {
    if (!this.isInitialized || !this.statusContainer) return;

    const statusHtml = Object.entries(status)
      .map(([key, value]) => `<span style="margin-right: 12px;"><strong>${key}:</strong> ${value}</span>`)
      .join('');

    this.statusContainer.innerHTML = statusHtml;
  }

  /**
   * 清空日志
   */
  public clearLogs(): void {
    this.logs = [];
    if (this.logContainer) {
      this.logContainer.innerHTML = '';
    }
    this.addLog('info', '日志已清空');
  }

  /**
   * 导出日志
   */
  public exportLogs(): void {
    const logText = this.logs
      .map(entry => `[${entry.timestamp}] ${entry.level.toUpperCase()}: ${entry.message}${entry.data ? '\n' + JSON.stringify(entry.data, null, 2) : ''}`)
      .join('\n\n');

    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `aistudio-debug-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    URL.revokeObjectURL(url);
    this.addLog('info', '日志已导出');
  }

  /**
   * 销毁面板
   */
  public destroy(): void {
    if (this.panel) {
      document.body.removeChild(this.panel);
      this.panel = null;
      this.logContainer = null;
      this.statusContainer = null;
    }
  }
}
