import logging
from datetime import UTC, datetime  # UTC from datetime

from fastapi import (
    Depends,
    HTTPException,
)

from server.core import Model<PERSON><PERSON>, ModelList, manager
from server.utils import verify_api_key

logger = logging.getLogger(__name__)


# --- API 端点 ---
async def list_models(_api_key: None = Depends(verify_api_key)):
    # 检查是否有任何用户脚本连接（Gemini 或 AI Studio）
    has_gemini_connection = manager.active_userscript is not None
    has_aistudio_connection = manager.has_aistudio_connections()

    if not has_gemini_connection and not has_aistudio_connection:
        raise HTTPException(status_code=503, detail="Userscript 未连接，无法获取模型列表。")

    # 优先使用 AI Studio 模型，如果没有则使用 Gemini 模型
    models = []
    if has_aistudio_connection and manager.aistudio_models:
        models = manager.aistudio_models
        logger.info(f"使用 AI Studio 模型列表: {len(models)} 个模型")
    elif has_gemini_connection and manager.available_models:
        models = manager.available_models
        logger.info(f"使用 Gemini 模型列表: {len(models)} 个模型")
    else:
        # 有连接但没有模型
        logger.warning("用户脚本已连接，但模型列表暂不可用。")
        raise HTTPException(status_code=503, detail="Userscript 已连接，但模型列表暂不可用，请稍后再试。")

    now = int(datetime.now(tz=UTC).timestamp())
    model_data_list = [
        ModelData(id=m.get("id", f"unknown-model-{i}"), created=now)
        for i, m in enumerate(models)
        if m.get("id")  # 确保模型有 ID
    ]

    logger.info(f"返回模型列表: {[m.id for m in model_data_list]}")
    return ModelList(data=model_data_list)
