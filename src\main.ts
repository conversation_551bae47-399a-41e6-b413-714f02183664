/**
 * AI Studio 2 API - Main Entry Point
 *
 * 用户脚本主入口文件
 */

import type {
  ApiRequestMessage,
  ModelInfo,
  GenerationConfig,
  ChatMessage
} from './types';
import { DEFAULT_CONFIG, FEATURE_FLAGS, VERSION_INFO } from './config/constants';
import { logger, delay } from './utils/helpers';
import { AuthManager } from './auth/auth-manager';
import { DOMController } from './dom/dom-controller';
import { RequestInterceptor } from './api/request-interceptor';
import { WebSocketClient } from './websocket/ws-client';
import { DebugPanel } from './debug/debug-panel';

class AiStudio2API {
  private authManager: AuthManager;
  private domController: DOMController;
  private requestInterceptor: RequestInterceptor;
  private wsClient: WebSocketClient;
  private debugPanel: DebugPanel | null = null;
  private isInitialized = false;
  private config = DEFAULT_CONFIG;

  constructor() {
    this.authManager = new AuthManager();
    this.domController = new DOMController();
    this.requestInterceptor = new RequestInterceptor();
    this.wsClient = new WebSocketClient(this.config.wsUrl);

    if (FEATURE_FLAGS.ENABLE_DEBUG_PANEL) {
      this.debugPanel = new DebugPanel();
    }

    this.bindMethods();
    this.setupEventListeners();
  }

  /**
   * 绑定方法到实例
   */
  private bindMethods(): void {
    this.initialize = this.initialize.bind(this);
    this.handleApiRequest = this.handleApiRequest.bind(this);
    this.handleModelsLoaded = this.handleModelsLoaded.bind(this);
  }



  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // WebSocket 事件
    this.wsClient.on('connected', () => {
      logger.info('WebSocket 连接成功');
      this.debugPanel?.updateStatus({ connection: 'connected' });
      this.debugPanel?.addLog('info', 'WebSocket 连接成功');
    });

    this.wsClient.on('disconnected', () => {
      logger.info('WebSocket 连接断开');
      this.debugPanel?.updateStatus({ connection: 'disconnected' });
      this.debugPanel?.addLog('warn', 'WebSocket 连接断开');
    });

    this.wsClient.on('apiRequest', this.handleApiRequest);
    this.wsClient.on('chatRequest', this.handleChatRequest);

    this.wsClient.on('error', (error) => {
      logger.error('WebSocket 错误:', error);
      this.debugPanel?.addLog('error', `WebSocket 错误: ${error.message}`);
    });

    // 请求拦截器事件
    this.requestInterceptor.on('modelsLoaded', this.handleModelsLoaded);

    this.requestInterceptor.on('responseReceived', (response) => {
      logger.debug('拦截到 API 响应:', response);
    });

    this.requestInterceptor.on('error', (error) => {
      logger.error('请求拦截器错误:', error);
      this.debugPanel?.addLog('error', `请求拦截错误: ${error.message}`);
    });

    // 调试面板已创建，无需额外事件监听器
    if (this.debugPanel) {
      logger.info('🎉 调试面板已创建');
    }
  }

  /**
   * 初始化应用
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('应用已初始化');
      return;
    }

    try {
      logger.info(`AI Studio 2 API v${VERSION_INFO.VERSION} 正在初始化...`);

      // 等待页面加载完成
      await this.waitForPageReady();

      // 初始化认证管理器
      if (!this.authManager.isAuthenticated()) {
        await this.authManager.refreshCredentials();
      }

      // 初始化 DOM 控制器
      await this.domController.initialize();

      // 启动请求拦截器
      this.requestInterceptor.start();

      // 调试面板已在构造函数中创建
      if (this.debugPanel) {
        this.debugPanel.show();
      }

      // 设置事件监听器
      this.setupEventListeners();

      // 自动连接到服务器
      if (this.config.autoConnect) {
        await this.connectToServer();
      }

      this.isInitialized = true;
      logger.info('AI Studio 2 API 初始化完成');

      // 注册菜单命令
      this.registerMenuCommands();

    } catch (error) {
      logger.error('初始化失败:', error);
      throw error;
    }
  }

  /**
   * 等待页面准备就绪
   */
  private async waitForPageReady(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'complete' || document.readyState === 'interactive') {
        resolve();
      } else {
        document.addEventListener('DOMContentLoaded', () => resolve(), { once: true });
      }
    });
  }

  /**
   * 连接到服务器
   */
  private async connectToServer(): Promise<void> {
    try {
      await this.wsClient.connect();

      // 发送模型列表（如果已拦截到）
      const models = this.requestInterceptor.getInterceptedModels();
      if (models.length > 0) {
        await this.wsClient.sendReadyMessage(models);
      }
    } catch (error) {
      logger.error('连接服务器失败:', error);
      this.debugPanel?.addLog('error', `连接失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 处理 API 请求
   */
  private async handleApiRequest(message: ApiRequestMessage): Promise<void> {
    const { requestId, data } = message;

    try {
      logger.info(`处理 API 请求 ${requestId}:`, data);
      this.debugPanel?.addLog('info', `收到 API 请求: ${data.model}`);

      // 切换模型（如果需要）
      if (data.model) {
        await this.switchModel(data.model);
      }

      // 设置生成参数
      const config: GenerationConfig = {};
      if (data.temperature !== undefined) config.temperature = data.temperature;
      if (data.maxTokens !== undefined) config.maxOutputTokens = data.maxTokens;
      if (data.topP !== undefined) config.topP = data.topP;
      if (data.stopSequences !== undefined) config.stopSequences = data.stopSequences;

      if (Object.keys(config).length > 0) {
        await this.domController.setGenerationConfig(config);
      }

      // 准备消息
      const prompt = this.preparePrompt(data.messages);

      // 发送消息
      await this.domController.sendMessage(prompt);

      // 处理响应
      if (data.stream) {
        await this.handleStreamResponse(requestId);
      } else {
        await this.handleNormalResponse(requestId);
      }

    } catch (error) {
      logger.error(`处理 API 请求 ${requestId} 失败:`, error);
      await this.wsClient.sendError(requestId, error instanceof Error ? error : new Error(String(error)));
      this.debugPanel?.addLog('error', `请求处理失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 处理聊天请求 - 新的完整实现
   */
  private async handleChatRequest(message: any): Promise<void> {
    const { requestId, data } = message;

    try {
      logger.info(`处理聊天请求: ${requestId}`);
      this.debugPanel?.addLog('info', `收到聊天请求: ${requestId}`);

      // 使用DOM控制器的新方法执行完整的聊天请求
      await this.domController.executeChatRequest(data);

      // 发送成功响应
      await this.wsClient.sendApiResponse(requestId, '聊天请求执行完成', 'stop');
      this.debugPanel?.addLog('info', `聊天请求 ${requestId} 执行完成`);

    } catch (error) {
      logger.error(`处理聊天请求 ${requestId} 失败:`, error);
      await this.wsClient.sendError(requestId, error instanceof Error ? error : new Error(String(error)));
      this.debugPanel?.addLog('error', `聊天请求处理失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 准备提示文本
   */
  private preparePrompt(messages: ChatMessage[]): string {
    const parts: string[] = [];

    for (const message of messages) {
      switch (message.role) {
        case 'system':
          parts.push(`系统指令: ${message.content}`);
          break;
        case 'user':
          parts.push(`用户: ${message.content}`);
          break;
        case 'assistant':
          parts.push(`助手: ${message.content}`);
          break;
      }
    }

    return parts.join('\n\n');
  }

  /**
   * 处理流式响应
   */
  private async handleStreamResponse(requestId: string): Promise<void> {
    let previousText = '';
    let isComplete = false;

    const checkForUpdates = async () => {
      while (!isComplete) {
        try {
          if (this.domController.isGenerating()) {
            const currentText = await this.domController.getResponseText();

            if (currentText.length > previousText.length) {
              const delta = currentText.substring(previousText.length);
              await this.wsClient.sendStreamChunk(requestId, delta);
              previousText = currentText;
            }
          } else {
            // 生成完成
            const finalText = await this.domController.getResponseText();

            if (finalText.length > previousText.length) {
              const delta = finalText.substring(previousText.length);
              await this.wsClient.sendStreamChunk(requestId, delta);
            }

            await this.wsClient.sendStreamEnd(requestId);
            isComplete = true;
            break;
          }

          await delay(200); // 轮询间隔
        } catch (error) {
          logger.error('流式响应处理错误:', error);
          await this.wsClient.sendError(requestId, error instanceof Error ? error : new Error(String(error)));
          isComplete = true;
          break;
        }
      }
    };

    await checkForUpdates();
  }

  /**
   * 处理普通响应
   */
  private async handleNormalResponse(requestId: string): Promise<void> {
    try {
      // 等待生成完成
      await this.domController.waitForGenerationComplete();

      // 获取响应文本
      const responseText = await this.domController.getResponseText();

      // 发送响应
      await this.wsClient.sendApiResponse(requestId, responseText);

      this.debugPanel?.addLog('info', `请求 ${requestId} 处理完成`);
    } catch (error) {
      logger.error('普通响应处理错误:', error);
      await this.wsClient.sendError(requestId, error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 处理模型列表加载
   */
  private handleModelsLoaded(models: ModelInfo[]): void {
    logger.info(`加载模型列表: ${models.length} 个模型`);

    this.domController.setModelList(models);

    // 更新调试面板状态
    if (this.debugPanel) {
      this.debugPanel.updateStatus({ models: models.length });
      this.debugPanel.addLog('info', `加载了 ${models.length} 个模型`);
    }

    // 如果已连接，发送模型列表到服务器
    if (this.wsClient.isConnected()) {
      this.wsClient.sendReadyMessage(models);
    }

    // 启动参数同步
    this.startParameterSync();
  }

  /**
   * 切换模型
   */
  private async switchModel(modelId: string): Promise<void> {
    try {
      await this.domController.switchModel(modelId);
      this.debugPanel?.addLog('info', `切换到模型: ${modelId}`);
    } catch (error) {
      logger.error('模型切换失败:', error);
      this.debugPanel?.addLog('error', `模型切换失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // 注释掉未使用的方法以避免编译错误
  // /**
  //  * 更新配置
  //  */
  // private async updateConfig(config: GenerationConfig): Promise<void> {
  //   try {
  //     await this.domController.setGenerationConfig(config);
  //     this.debugPanel?.addLog('info', '生成参数已更新');
  //   } catch (error) {
  //     logger.error('配置更新失败:', error);
  //     this.debugPanel?.addLog('error', `配置更新失败: ${error instanceof Error ? error.message : String(error)}`);
  //   }
  // }

  // /**
  //  * 发送测试消息
  //  */
  // private async sendTestMessage(message: string): Promise<void> {
  //   try {
  //     await this.domController.sendMessage(message);
  //     this.debugPanel?.addLog('info', `发送测试消息: ${message.substring(0, 50)}...`);
  //   } catch (error) {
  //     logger.error('发送测试消息失败:', error);
  //     this.debugPanel?.addLog('error', `发送失败: ${error instanceof Error ? error.message : String(error)}`);
  //   }
  // }

  /**
   * 启动参数同步
   */
  private startParameterSync(): void {
    // 延迟启动，确保页面稳定
    setTimeout(() => {
      this.syncParametersFromPage();

      // 定期同步页面参数到调试面板 - 更快的同步频率
      setInterval(() => {
        this.syncParametersFromPage();
      }, 1000); // 每1秒同步一次，更快响应
    }, 1000); // 减少初始延迟
  }

  /**
   * 从页面同步参数到调试面板
   */
  private syncParametersFromPage(): void {
    try {
      // const currentParams = this.domController.getCurrentParameters();
      const currentModel = this.domController.getCurrentModel();

      // 更新调试面板的参数值
      if (this.debugPanel) {
        this.debugPanel.updateStatus({
          lastActivity: 'Parameter sync',
          currentModel: currentModel || undefined
        });
      }
    } catch (error) {
      logger.debug('参数同步失败:', error);
    }
  }

  /**
   * 注册菜单命令
   */
  private registerMenuCommands(): void {
    if (window.GM_registerMenuCommand) {
      window.GM_registerMenuCommand('显示/隐藏调试面板', () => {
        this.debugPanel?.toggle();
      });

      window.GM_registerMenuCommand('连接到服务器', () => {
        this.connectToServer();
      });

      window.GM_registerMenuCommand('断开连接', () => {
        this.wsClient.disconnect();
      });

      window.GM_registerMenuCommand('刷新认证', async () => {
        try {
          await this.authManager.refreshCredentials();
          this.debugPanel?.addLog('info', '认证刷新成功');
        } catch (error) {
          this.debugPanel?.addLog('error', `认证刷新失败: ${error instanceof Error ? error.message : String(error)}`);
        }
      });
    }
  }
}

// 创建全局实例
const aiStudio2API = new AiStudio2API();

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    aiStudio2API.initialize().catch(error => {
      logger.error('应用初始化失败:', error);
    });
  });
} else {
  aiStudio2API.initialize().catch(error => {
    logger.error('应用初始化失败:', error);
  });
}

// 导出到全局作用域（用于调试）
(window as any).aiStudio2API = aiStudio2API;
